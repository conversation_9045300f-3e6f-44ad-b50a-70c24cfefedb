from django.urls import reverse
from django.test import TestCase
from django.contrib.messages import get_messages
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

from .models import *


class BookModelTest(TestCase):
    """
    Test cases for Book model CRUD operations.

    Tests the basic database operations for the Book model including
    creation, reading, updating, and deletion of book records.
    """

    def setUp(self):
        """Set up test data for Book model tests."""
        # Create test Author and Genre
        self.author = Author.objects.create(author_name="Test Author")
        self.genre = Genre.objects.create(genre_name="Test Genre")

        # Create a Book instance
        self.book = Book.objects.create(
            title="Test Book",
            author=self.author,
            publication_year=2020,
            isbn="1234567890",
            description="This is a test book.",
            total_copies=10,
            copies_available=5,
            genre=self.genre,
            availability=True
        )

    def test_create_book(self):
        """Test that a Book is created correctly with all required fields."""
        book_count = Book.objects.count()
        self.assertEqual(book_count, 1)
        self.assertEqual(self.book.title, "Test Book")
        self.assertEqual(self.book.copies_available, 5)
        self.assertTrue(self.book.availability)

    def test_read_book(self):
        """Test fetching a Book from database and verifying relationships."""
        book = Book.objects.get(pk=self.book.book_id)
        self.assertEqual(book.author.author_name, "Test Author")
        self.assertEqual(book.genre.genre_name, "Test Genre")

    def test_update_book(self):
        """Test updating Book fields and persisting changes."""
        self.book.copies_available = 8
        self.book.title = "Updated Book"
        self.book.save()

        updated_book = Book.objects.get(pk=self.book.book_id)
        self.assertEqual(updated_book.copies_available, 8)
        self.assertEqual(updated_book.title, "Updated Book")

    def test_delete_book(self):
        """Test deleting a Book and verifying removal from database."""
        self.book.delete()
        book_count = Book.objects.count()
        self.assertEqual(book_count, 0)
class SignUpViewTest(TestCase):
    """
    Test cases for user registration/signup functionality.

    Tests both successful signup scenarios and various failure cases
    including validation errors and password mismatches.
    """

    def test_signup_success(self):
        """Test successful user registration with valid data."""
        response = self.client.post(reverse("signup"), {
            "username": "viewtest",
            "first_name": "View",
            "last_name": "Test",
            "gender": "M",
            "bio": "This is a test bio",
            "email": "<EMAIL>",
            "password": "mypassword123",
            "confirm_password": "mypassword123"
        })
        self.assertEqual(response.status_code, 302)  # Redirects on success
        self.assertTrue(User.objects.filter(username="viewtest").exists())

    def test_signup_failure(self):
        """Test signup failure with missing required fields and password mismatch."""
        response = self.client.post(reverse("signup"), {
            "username": "viewtest",
            "email": "<EMAIL>",
            "password": "mypassword123",
            "confirm_password": "wrongpassword"
        })
        self.assertEqual(response.status_code, 200)  # Stays on signup page
        self.assertFalse(User.objects.filter(username="viewtest").exists())

        # Capture and verify error messages
        messages = list(get_messages(response.wsgi_request))
        message_texts = [str(m) for m in messages]

        # Assert custom error messages are displayed
        self.assertIn("First Name and Last Name are required.", message_texts)
        self.assertIn("Please select a valid gender.", message_texts)
        self.assertIn("Passwords do not match.", message_texts)
        

class BorrowBookIntegrationTest(TestCase):
    """
    Integration tests for book borrowing functionality.

    Tests the complete flow of borrowing books including success scenarios,
    error handling, and database state changes.
    """

    def setUp(self):
        """Set up test data for book borrowing integration tests."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', password='testpass123'
        )

        # Create related objects
        self.author = Author.objects.create(author_name='Test Author')
        self.genre = Genre.objects.create(genre_name='Fiction')

        # Create a test book
        self.book = Book.objects.create(
            title='Test Book',
            author=self.author,
            genre=self.genre,
            availability=True,
            publication_year=2001,
            isbn='1234567890',
            description='Test Description',
            total_copies=1,
            copies_available=1,
        )

    def test_borrow_book_success(self):
        """Test successful book borrowing flow."""
        # Log in the user
        self.client.login(username='testuser', password='testpass123')

        # Submit borrow request
        response = self.client.post(reverse('borrow_book', args=[self.book.book_id]))

        # Verify redirect on success
        self.assertEqual(response.status_code, 302)

        # Verify success message
        messages = list(get_messages(response.wsgi_request))
        message_texts = [str(m) for m in messages]
        self.assertIn(f"Book '{self.book.title}' has been borrowed successfully!", message_texts)

        # Verify loan record was created
        loan = Loan.objects.filter(user=self.user, book=self.book).first()
        self.assertIsNotNone(loan)

        # Verify book availability was updated
        self.book.refresh_from_db()
        self.assertFalse(self.book.availability)

    def test_borrow_book_already_borrowed(self):
        """Test borrowing a book that is already unavailable."""
        # Set book as unavailable
        self.book.availability = False
        self.book.save()

        # Log in and attempt to borrow
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(reverse('borrow_book', args=[self.book.book_id]))

        # Verify no loan record was created
        self.assertFalse(Loan.objects.filter(user=self.user, book=self.book).exists())

        # Verify error message is displayed
        messages = list(get_messages(response.wsgi_request))
        self.assertIn(f"Book '{self.book.title}' is not available for borrowing.", [str(m) for m in messages])
class SignUpSystemTest(StaticLiveServerTestCase):
    """
    End-to-end system tests using Selenium WebDriver.

    Tests the complete user journey from signup to book borrowing
    using a real browser to simulate user interactions.
    """

    @classmethod
    def setUpClass(cls):
        """Set up the browser for system tests."""
        super().setUpClass()
        cls.browser = webdriver.Chrome()

    def setUp(self):
        """Set up test data for system tests."""
        # Create test author and genre
        self.author = Author.objects.create(author_name='Test Author')
        self.genre = Genre.objects.create(genre_name='Fiction')

        # Create a test book for borrowing
        self.book = Book.objects.create(
            title='Test Book',
            author=self.author,
            genre=self.genre,
            availability=True,
            publication_year=2001,
            isbn='1234567890',
            description='Test Description',
            total_copies=1,
            copies_available=1,
        )

    @classmethod
    def tearDownClass(cls):
        """Clean up browser after all tests."""
        cls.browser.quit()
        super().tearDownClass()

    def test_user_signup_flow(self):
        """
        Test complete user journey: signup -> login -> browse books -> borrow book.

        This end-to-end test simulates a real user workflow using Selenium
        to interact with the web interface.
        """
        # Step 1: User Signup
        self.browser.get(f'{self.live_server_url}/signup/')

        # Fill signup form
        self.browser.find_element(By.NAME, 'username').send_keys('sysuser')
        self.browser.find_element(By.NAME, 'email').send_keys('<EMAIL>')
        self.browser.find_element(By.NAME, 'first_name').send_keys('John')
        self.browser.find_element(By.NAME, 'last_name').send_keys('Doe')
        self.browser.find_element(By.NAME, 'password').send_keys('password123')
        self.browser.find_element(By.NAME, 'gender').send_keys('M')
        self.browser.find_element(By.NAME, 'confirm_password').send_keys('password123')

        # Submit signup form
        time.sleep(4)  # Wait for form to be ready
        self.browser.find_element(By.XPATH, '//button[text()="Sign Up"]').click()

        time.sleep(7)  # Wait for page transition
        # Verify signup success
        self.assertIn('/signup_success/', self.browser.current_url)
        time.sleep(7)  # Wait for page transition

        # Step 2: User Login
        self.browser.get(f'{self.live_server_url}/login/')
        self.browser.find_element(By.NAME, 'username').send_keys('sysuser')
        self.browser.find_element(By.NAME, 'password').send_keys('password123')
        time.sleep(4)
        self.browser.find_element(By.XPATH, '//button[text()="Login"]').click()
        time.sleep(4)

        # Step 3: Browse Books
        self.browser.get(f'{self.live_server_url}/browse_books/')
        time.sleep(4)
        self.assertIn('Browse Books by Category', self.browser.page_source)

        # Click on first available borrow link
        borrow_link = WebDriverWait(self.browser, 30).until(
            EC.element_to_be_clickable(
                (By.XPATH, '(//a[contains(normalize-space(text()), "Borrow")])[1]')
            )
        )
        self.browser.execute_script("arguments[0].scrollIntoView(true);", borrow_link)
        self.browser.execute_script("arguments[0].click();", borrow_link)
        time.sleep(10)

        # Step 4: Borrow Book
        self.assertIn('/book/', self.browser.current_url)

        # Find and click the borrow button on book detail page
        borrow_link = WebDriverWait(self.browser, 30).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "a.btn-success"))
        )
        self.browser.execute_script("arguments[0].scrollIntoView(true);", borrow_link)
        self.browser.execute_script("arguments[0].click();", borrow_link)

        # Handle JavaScript confirmation dialog
        alert = WebDriverWait(self.browser, 5).until(EC.alert_is_present())
        alert.accept()  # Accept the confirmation
        time.sleep(4)

        # Verify borrow success message
        self.assertIn('has been borrowed successfully!', self.browser.page_source)
       